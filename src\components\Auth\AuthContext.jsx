import React, { createContext, useState, useContext, useEffect } from 'react';
import client from '../../../api/apolloClient';

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(() => {
    const storedUser = localStorage.getItem('agent');
    return storedUser ? JSON.parse(storedUser) : null;
  });
  const [token, setToken] = useState(localStorage.getItem('token'));

  useEffect(() => {
    if (token) {
      localStorage.setItem('token', token);
    } else {
      localStorage.removeItem('token');
    }
  }, [token]);

  useEffect(() => {
    if (user) {
      localStorage.setItem('agent', JSON.stringify(user));
    } else {
      localStorage.removeItem('agent');
    }
  }, [user]);

  const login = (userData, authToken) => {
    setUser(userData);
    setToken(authToken);
  };

  const logout = () => {
    setUser(null);          // Clear user from local storage
    setToken(null);         // Clear token from local storage
    if (client) {  
      client.resetStore();  // Clear client cache
    }
  };

  const isAuthenticated = () => {
    return !!token;
  };

  const isAdmin = () => {
    return user?.role === 'ADMIN';
  };

  const isDeveloper = () => {
    return user?.role === 'DEVELOPER';
  };

  const hasClearance = (level) => {
    if (!user?.clearance) return false;
    return user.clearance >= level;
  };

  return (
    <AuthContext.Provider value={{ user, token, login, logout, isAuthenticated, isAdmin, isDeveloper, hasClearance }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  return useContext(AuthContext);
} 